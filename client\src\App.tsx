import { useState, useEffect } from "react";
import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { CookieConsent } from "@/components/CookieConsent";
import { SEOHead } from "@/components/SEOHead";
import { type Language } from "@/lib/i18n";

// Pages
import Home from "@/pages/Home";
import Tools from "@/pages/Tools";
import Blog from "@/pages/Blog";
import Privacy from "@/pages/Privacy";
import Terms from "@/pages/Terms";
import Help from "@/pages/Help";
import Contact from "@/pages/Contact";
import Status from "@/pages/Status";
import Feedback from "@/pages/Feedback";
import Cookies from "@/pages/Cookies";
import Imprint from "@/pages/Imprint";
import NotFound from "@/pages/not-found";

function Router({ language, onLanguageChange }: { language: Language; onLanguageChange: (lang: Language) => void }) {
  const [location] = useLocation();
  
  // Extract language from URL path (e.g., /en/, /zh/, etc.)
  const pathLanguage = location.split('/')[1] as Language;
  
  useEffect(() => {
    if (['en', 'zh', 'es', 'fr', 'de'].includes(pathLanguage) && pathLanguage !== language) {
      onLanguageChange(pathLanguage);
    }
  }, [pathLanguage, language, onLanguageChange]);

  return (
    <>
      <SEOHead language={language} />
      <div className="min-h-screen flex flex-col">
        <Header language={language} onLanguageChange={onLanguageChange} />
        <main className="flex-1">
          <Switch>
            {/* Language-specific routes */}
            <Route path="/" component={() => <Home language={language} />} />
            <Route path="/en" component={() => <Home language={language} />} />
            <Route path="/zh" component={() => <Home language={language} />} />
            <Route path="/es" component={() => <Home language={language} />} />
            <Route path="/fr" component={() => <Home language={language} />} />
            <Route path="/de" component={() => <Home language={language} />} />
            
            {/* Feature pages */}
            <Route path="/tools" component={() => <Tools language={language} />} />
            <Route path="/blog" component={() => <Blog language={language} />} />
            
            {/* Support pages - Base routes */}
            <Route path="/help" component={() => <Help language={language} />} />
            <Route path="/contact" component={() => <Contact language={language} />} />
            <Route path="/status" component={() => <Status language={language} />} />
            <Route path="/feedback" component={() => <Feedback language={language} />} />

            {/* Support pages - Language-specific routes */}
            <Route path="/en/help" component={() => <Help language={language} />} />
            <Route path="/zh/help" component={() => <Help language={language} />} />
            <Route path="/es/help" component={() => <Help language={language} />} />
            <Route path="/fr/help" component={() => <Help language={language} />} />
            <Route path="/de/help" component={() => <Help language={language} />} />

            <Route path="/en/contact" component={() => <Contact language={language} />} />
            <Route path="/zh/contact" component={() => <Contact language={language} />} />
            <Route path="/es/contact" component={() => <Contact language={language} />} />
            <Route path="/fr/contact" component={() => <Contact language={language} />} />
            <Route path="/de/contact" component={() => <Contact language={language} />} />

            <Route path="/en/status" component={() => <Status language={language} />} />
            <Route path="/zh/status" component={() => <Status language={language} />} />
            <Route path="/es/status" component={() => <Status language={language} />} />
            <Route path="/fr/status" component={() => <Status language={language} />} />
            <Route path="/de/status" component={() => <Status language={language} />} />

            <Route path="/en/feedback" component={() => <Feedback language={language} />} />
            <Route path="/zh/feedback" component={() => <Feedback language={language} />} />
            <Route path="/es/feedback" component={() => <Feedback language={language} />} />
            <Route path="/fr/feedback" component={() => <Feedback language={language} />} />
            <Route path="/de/feedback" component={() => <Feedback language={language} />} />

            {/* Legal pages - Base routes */}
            <Route path="/privacy" component={() => <Privacy language={language} />} />
            <Route path="/terms" component={() => <Terms language={language} />} />
            <Route path="/cookies" component={() => <Cookies language={language} />} />
            <Route path="/imprint" component={() => <Imprint language={language} />} />

            {/* Legal pages - Language-specific routes */}
            <Route path="/en/privacy" component={() => <Privacy language={language} />} />
            <Route path="/zh/privacy" component={() => <Privacy language={language} />} />
            <Route path="/es/privacy" component={() => <Privacy language={language} />} />
            <Route path="/fr/privacy" component={() => <Privacy language={language} />} />
            <Route path="/de/privacy" component={() => <Privacy language={language} />} />

            <Route path="/en/terms" component={() => <Terms language={language} />} />
            <Route path="/zh/terms" component={() => <Terms language={language} />} />
            <Route path="/es/terms" component={() => <Terms language={language} />} />
            <Route path="/fr/terms" component={() => <Terms language={language} />} />
            <Route path="/de/terms" component={() => <Terms language={language} />} />

            <Route path="/en/cookies" component={() => <Cookies language={language} />} />
            <Route path="/zh/cookies" component={() => <Cookies language={language} />} />
            <Route path="/es/cookies" component={() => <Cookies language={language} />} />
            <Route path="/fr/cookies" component={() => <Cookies language={language} />} />
            <Route path="/de/cookies" component={() => <Cookies language={language} />} />

            <Route path="/en/imprint" component={() => <Imprint language={language} />} />
            <Route path="/zh/imprint" component={() => <Imprint language={language} />} />
            <Route path="/es/imprint" component={() => <Imprint language={language} />} />
            <Route path="/fr/imprint" component={() => <Imprint language={language} />} />
            <Route path="/de/imprint" component={() => <Imprint language={language} />} />

            {/* Additional routes */}
            <Route path="/enterprise" component={() => <div className="container mx-auto py-16 text-center"><h1 className="text-4xl font-bold">Enterprise Solutions - Coming Soon</h1></div>} />
            
            {/* Fallback to 404 */}
            <Route component={NotFound} />
          </Switch>
        </main>
        <Footer language={language} />
        <CookieConsent language={language} />
      </div>
    </>
  );
}

function App() {
  const [language, setLanguage] = useState<Language>('en');

  // Initialize language from localStorage or browser language
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['en', 'zh', 'es', 'fr', 'de'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
    } else {
      // Detect browser language
      const browserLang = navigator.language.split('-')[0] as Language;
      if (['en', 'zh', 'es', 'fr', 'de'].includes(browserLang)) {
        setLanguage(browserLang);
      }
    }
  }, []);

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
    
    // Update URL to include language prefix
    const currentPath = window.location.pathname;
    const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
    const newPath = newLanguage === 'en' ? pathWithoutLang : `/${newLanguage}${pathWithoutLang}`;
    window.history.replaceState({}, '', newPath);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router language={language} onLanguageChange={handleLanguageChange} />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
