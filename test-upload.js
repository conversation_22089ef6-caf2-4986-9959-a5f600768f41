// Test script to verify image upload functionality
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testUpload(filePath) {
    try {
        console.log(`Testing upload for: ${filePath}`);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ File not found: ${filePath}`);
            return;
        }

        const form = new FormData();
        form.append('image', fs.createReadStream(filePath));

        const response = await fetch('http://localhost:5000/api/upload', {
            method: 'POST',
            body: form
        });

        if (response.ok) {
            const result = await response.json();
            console.log(`✅ Upload successful: ${path.basename(filePath)} - ID: ${result.id}`);
        } else {
            const errorText = await response.text();
            console.log(`❌ Upload failed: ${path.basename(filePath)} - ${response.status}: ${errorText}`);
        }
    } catch (error) {
        console.log(`❌ Error uploading ${path.basename(filePath)}: ${error.message}`);
    }
}

async function runTests() {
    console.log('🧪 Testing image upload functionality...\n');
    
    // Test with existing uploaded files
    const uploadsDir = './uploads';
    if (fs.existsSync(uploadsDir)) {
        const files = fs.readdirSync(uploadsDir);
        const imageFiles = files.filter(file => 
            !file.startsWith('processed_') && 
            /\.(jpg|jpeg|png|gif|webp|bmp|tiff|tif|svg)$/i.test(file)
        );
        
        if (imageFiles.length > 0) {
            console.log('Testing with existing files in uploads directory:');
            for (const file of imageFiles.slice(0, 3)) { // Test first 3 files
                await testUpload(path.join(uploadsDir, file));
            }
        } else {
            console.log('No existing image files found in uploads directory');
        }
    }
    
    console.log('\n✨ Test completed!');
    console.log('\nSupported formats:');
    console.log('- JPG/JPEG');
    console.log('- PNG');
    console.log('- GIF');
    console.log('- WebP');
    console.log('- BMP');
    console.log('- TIFF');
    console.log('- SVG');
}

runTests().catch(console.error);
