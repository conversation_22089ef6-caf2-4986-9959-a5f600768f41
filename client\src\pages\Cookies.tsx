import { useTranslation, type Language } from "@/lib/i18n";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Setting<PERSON>, Shield, Eye, ArrowLeft } from "lucide-react";
import { <PERSON> } from "wouter";

interface CookiesProps {
  language: Language;
}

export default function Cookies({ language }: CookiesProps) {
  const { t } = useTranslation(language);

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("cookies.page.back")}
            </Button>
          </Link>
        </div>
        {/* <PERSON>er */}
        <div className="text-center mb-16">
          <div className="gradient-bg rounded-lg w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Cookie className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("cookies.page.title")}
          </h1>
          <p className="text-xl text-gray-600">
            {t("cookies.page.description")}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {t("cookies.page.last_updated")}
          </p>
        </div>

        <div className="space-y-8">
          {/* What are Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-6 w-6 text-purple-600" />
                <span>{t("cookies.page.what_are_cookies")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="prose max-w-none">
              <p>
                {t("cookies.page.what_content")}
              </p>
            </CardContent>
          </Card>

          {/* How we use Cookies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-purple-600" />
                <span>{t("cookies.page.how_we_use")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">{t("cookies.page.essential_cookies")}</h3>
                <p className="text-gray-600">
                  {t("cookies.page.essential_content")}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">{t("cookies.page.analytics_cookies")}</h3>
                <p className="text-gray-600">
                  {t("cookies.page.analytics_content")}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">{t("cookies.page.preference_cookies")}</h3>
                <p className="text-gray-600">
                  {t("cookies.page.preference_content")}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Cookie Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-purple-600" />
                <span>{t("cookies.page.manage_cookies")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                {t("cookies.page.manage_content")}
              </p>
              <div>
                <h3 className="font-semibold mb-2">{t("cookies.page.browser_settings")}</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Chrome: Settings {'>'} Advanced {'>'} Privacy and security {'>'} Site Settings {'>'} Cookies</li>
                  <li>Firefox: Preferences {'>'} Privacy & Security {'>'} Cookies and Site Data</li>
                  <li>Safari: Preferences {'>'} Privacy {'>'} Manage Website Data</li>
                  <li>Edge: Settings {'>'} Site permissions {'>'} Cookies and site data</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}