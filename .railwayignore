# Railway ignore file - 减少部署包大小

# Development files
.env.local
.env.development
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Test files
**/*.test.*
**/*.spec.*
test/
tests/
__tests__/

# Documentation (keep README.md)
docs/
*.md
!README.md
!DEPLOYMENT.md

# Development dependencies (Railway will install from package.json)
node_modules/

# Build artifacts that will be regenerated
dist/
build/
.next/
.nuxt/

# Temporary files
tmp/
temp/
uploads/*
!uploads/.gitkeep

# Git
.git/
.gitignore

# Other
coverage/
.nyc_output/