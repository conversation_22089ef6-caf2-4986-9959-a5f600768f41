# Railway 部署指南

## 准备工作

### 1. 创建Railway账户
访问 [railway.app](https://railway.app) 并使用GitHub账户登录

### 2. 准备环境变量
在Railway项目中设置以下环境变量：

**必需变量：**
- `NODE_ENV=production`
- `DATABASE_URL` (Railway会自动提供PostgreSQL数据库URL)

**可选变量：**
- `REMOVE_BG_API_KEY=your_api_key_here` (用于生产模式背景移除)

## 部署步骤

### 方法1：通过GitHub (推荐)

1. **推送代码到GitHub**
```bash
git add .
git commit -m "Prepare for Railway deployment"
git push origin main
```

2. **在Railway中创建项目**
- 登录Railway控制台
- 点击 "New Project"
- 选择 "Deploy from GitHub repo"
- 选择你的RemoveBg仓库

3. **添加PostgreSQL数据库**
- 在项目中点击 "Add Service"
- 选择 "PostgreSQL"
- Railway会自动设置DATABASE_URL环境变量

4. **配置环境变量**
- 进入项目设置
- 添加环境变量：
  - `NODE_ENV=production`
  - `REMOVE_BG_API_KEY=your_key` (可选)

5. **部署**
- Railway会自动检测到railway.toml配置
- 自动运行构建和部署

### 方法2：通过Railway CLI

1. **安装Railway CLI**
```bash
npm install -g @railway/cli
```

2. **登录并初始化**
```bash
railway login
railway init
```

3. **添加PostgreSQL**
```bash
railway add postgresql
```

4. **设置环境变量**
```bash
railway variables set NODE_ENV=production
railway variables set REMOVE_BG_API_KEY=your_key_here
```

5. **部署**
```bash
railway up
```

## 数据库设置

部署后需要推送数据库schema：

```bash
# 本地设置DATABASE_URL (从Railway获取)
export DATABASE_URL="postgresql://..."

# 推送数据库schema
npm run db:push
```

## 验证部署

1. **检查服务状态**
   - 访问Railway控制台查看部署日志
   - 确认服务正在运行

2. **测试功能**
   - 访问部署的URL
   - 测试图片上传功能
   - 检查多语言支持

3. **监控**
   - 查看Railway控制台的指标
   - 监控内存和CPU使用情况

## 故障排除

### 常见问题

1. **构建失败**
```bash
# 检查本地构建
npm run build
```

2. **数据库连接问题**
- 确认DATABASE_URL环境变量正确设置
- 检查数据库服务是否运行

3. **文件上传问题**
- 确认uploads目录权限
- 检查文件大小限制

4. **内存不足**
- Railway免费版限制512MB RAM
- 考虑优化图片处理逻辑

### 日志查看
```bash
# 使用Railway CLI查看日志
railway logs
```

## 成本优化

### 免费额度
- Railway提供$5/月免费额度
- 512MB RAM限制
- 通常足够小型应用使用

### 优化建议
1. **图片自动清理**：确保24小时后删除处理过的图片
2. **缓存优化**：使用适当的HTTP缓存头
3. **压缩**：启用gzip压缩
4. **监控使用量**：定期检查Railway控制台的使用统计

## 域名配置

### 自定义域名
1. 在Railway项目设置中添加自定义域名
2. 配置DNS记录指向Railway提供的地址
3. Railway会自动处理SSL证书

### 示例DNS配置
```
Type: CNAME
Name: www
Value: your-app.railway.app
```

## 环境变量完整列表

```env
# 必需
NODE_ENV=production
DATABASE_URL=postgresql://user:pass@host:port/db

# 可选
REMOVE_BG_API_KEY=your_remove_bg_api_key
PORT=5000  # Railway会自动设置
```

## 监控和维护

1. **定期检查**
   - 每周查看Railway控制台
   - 监控资源使用情况
   - 检查错误日志

2. **更新部署**
   - 推送到GitHub会自动触发重新部署
   - 或使用 `railway up` 手动部署

3. **备份**
   - 定期备份数据库
   - 使用 `railway db dump` 导出数据