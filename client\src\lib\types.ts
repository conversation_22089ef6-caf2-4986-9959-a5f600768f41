export interface SampleImage {
  id: string;
  url: string;
  category: 'people' | 'products' | 'animals' | 'cars' | 'graphics';
  alt: string;
}

export interface ProcessingStatus {
  id: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  originalPath?: string;
  processedPath?: string;
}

export interface Testimonial {
  id: string;
  company: string;
  companyLogo?: string;
  quote: string;
  author: string;
  authorTitle: string;
  authorAvatar?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  image: string;
  slug: string;
}

export interface UseCase {
  id: string;
  title: string;
  description: string;
  icon: string;
  href: string;
}
