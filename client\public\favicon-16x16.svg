<?xml version="1.0" encoding="UTF-8"?>
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="gradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#a855f7" />
      <stop offset="100%" stop-color="#ec4899" />
    </linearGradient>
  </defs>
  
  <!-- 圆角矩形背景 -->
  <rect width="16" height="16" rx="4" fill="url(#gradient16)" />
  
  <!-- 简化的剪刀图标 -->
  <circle cx="8" cy="4" r="1.5" stroke="white" stroke-width="1" fill="none"/>
  <circle cx="8" cy="12" r="1.5" stroke="white" stroke-width="1" fill="none"/>
  <path d="M11 4L5 10" stroke="white" stroke-width="1" stroke-linecap="round"/>
  <path d="M5 6L11 12" stroke="white" stroke-width="1" stroke-linecap="round"/>
</svg>
