import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { type Language } from '@/lib/i18n';
import { 
  defaultSEOConfig, 
  generateCanonicalUrl, 
  generateHreflangLinks, 
  generateJsonLd,
  type SEOConfig 
} from '@/lib/seo';

interface UseSEOOptions extends Partial<SEOConfig> {
  language: Language;
}

export const useSEO = (options: UseSEOOptions) => {
  const [location] = useLocation();
  const { language, ...customConfig } = options;
  
  const seoConfig = { ...defaultSEOConfig[language], ...customConfig };
  
  useEffect(() => {
    // Update document title
    if (seoConfig.title) {
      document.title = seoConfig.title;
    }
    
    // Update meta description
    if (seoConfig.description) {
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', seoConfig.description);
      }
    }
    
    // Update meta keywords
    if (seoConfig.keywords) {
      let metaKeywords = document.querySelector('meta[name="keywords"]');
      if (metaKeywords) {
        metaKeywords.setAttribute('content', seoConfig.keywords);
      } else {
        metaKeywords = document.createElement('meta');
        metaKeywords.setAttribute('name', 'keywords');
        metaKeywords.setAttribute('content', seoConfig.keywords);
        document.head.appendChild(metaKeywords);
      }
    }
    
    // Update canonical URL
    const canonicalUrl = seoConfig.canonical || generateCanonicalUrl(location, language);
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', canonicalUrl);
    
    // Update hreflang links
    const existingHreflangLinks = document.querySelectorAll('link[rel="alternate"][hreflang]');
    existingHreflangLinks.forEach(link => link.remove());
    
    const hreflangLinks = generateHreflangLinks(location);
    hreflangLinks.forEach(({ hreflang, href }) => {
      const link = document.createElement('link');
      link.setAttribute('rel', 'alternate');
      link.setAttribute('hreflang', hreflang);
      link.setAttribute('href', href);
      document.head.appendChild(link);
    });
    
    // Update Open Graph tags
    if (seoConfig.title) {
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', seoConfig.title);
      }
    }
    
    if (seoConfig.description) {
      const ogDescription = document.querySelector('meta[property="og:description"]');
      if (ogDescription) {
        ogDescription.setAttribute('content', seoConfig.description);
      }
    }
    
    const ogUrl = document.querySelector('meta[property="og:url"]');
    if (ogUrl) {
      ogUrl.setAttribute('content', canonicalUrl);
    }
    
    if (seoConfig.ogImage) {
      let ogImage = document.querySelector('meta[property="og:image"]');
      if (ogImage) {
        ogImage.setAttribute('content', seoConfig.ogImage);
      } else {
        ogImage = document.createElement('meta');
        ogImage.setAttribute('property', 'og:image');
        ogImage.setAttribute('content', seoConfig.ogImage);
        document.head.appendChild(ogImage);
      }
    }
    
    // Update Twitter Card tags
    if (seoConfig.title) {
      const twitterTitle = document.querySelector('meta[name="twitter:title"]');
      if (twitterTitle) {
        twitterTitle.setAttribute('content', seoConfig.title);
      }
    }
    
    if (seoConfig.description) {
      const twitterDescription = document.querySelector('meta[name="twitter:description"]');
      if (twitterDescription) {
        twitterDescription.setAttribute('content', seoConfig.description);
      }
    }
    
    if (seoConfig.ogImage) {
      let twitterImage = document.querySelector('meta[name="twitter:image"]');
      if (twitterImage) {
        twitterImage.setAttribute('content', seoConfig.ogImage);
      } else {
        twitterImage = document.createElement('meta');
        twitterImage.setAttribute('name', 'twitter:image');
        twitterImage.setAttribute('content', seoConfig.ogImage);
        document.head.appendChild(twitterImage);
      }
    }
    
    // Update JSON-LD
    const jsonLd = seoConfig.jsonLd || generateJsonLd(language, location);
    let existingJsonLd = document.querySelector('script[type="application/ld+json"]#dynamic-jsonld');
    if (existingJsonLd) {
      existingJsonLd.remove();
    }
    
    const jsonLdScript = document.createElement('script');
    jsonLdScript.setAttribute('type', 'application/ld+json');
    jsonLdScript.setAttribute('id', 'dynamic-jsonld');
    jsonLdScript.textContent = JSON.stringify(jsonLd);
    document.head.appendChild(jsonLdScript);
    
    // Update language attribute
    document.documentElement.setAttribute('lang', language);
    
  }, [seoConfig, language, location]);
  
  return {
    seoConfig,
    canonicalUrl: seoConfig.canonical || generateCanonicalUrl(location, language),
    hreflangLinks: generateHreflangLinks(location),
    jsonLd: seoConfig.jsonLd || generateJsonLd(language, location)
  };
};
