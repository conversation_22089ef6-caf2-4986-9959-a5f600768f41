import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { Shield, Eye, Lock, UserCheck, FileText, Mail, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface PrivacyProps {
  language: Language;
}

export default function Privacy({ language }: PrivacyProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "information-collection",
      title: t("privacy.section.collection.title"),
      icon: <Eye className="h-6 w-6" />,
      content: t("privacy.section.collection.content")
    },
    {
      id: "information-use",
      title: t("privacy.section.use.title"),
      icon: <UserCheck className="h-6 w-6" />,
      content: t("privacy.section.use.content")
    },
    {
      id: "data-security",
      title: t("privacy.section.security.title"),
      icon: <Lock className="h-6 w-6" />,
      content: t("privacy.section.security.content")
    },
    {
      id: "data-sharing",
      title: t("privacy.section.sharing.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("privacy.section.sharing.content")
    },
    {
      id: "your-rights",
      title: t("privacy.section.rights.title"),
      icon: <FileText className="h-6 w-6" />,
      content: t("privacy.section.rights.content")
    },
    {
      id: "contact",
      title: t("privacy.section.contact.title"),
      icon: <Mail className="h-6 w-6" />,
      content: t("privacy.section.contact.content")
    }
  ];

  return (
    <>
      <SEOHead
        title="Privacy Policy - Remove bg"
        description="Learn how Remove bg protects your privacy and handles your personal data. Comprehensive privacy policy covering data collection, usage, and your rights."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t("privacy.page.back")}
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Shield className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                {t("privacy.page.title")}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t("privacy.page.description")}
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>{t("privacy.page.last_updated")}</p>
                <p>{t("privacy.page.effective_date")}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Privacy Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Overview */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">{t("privacy.page.quick_overview")}</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  <strong>{t("privacy.page.overview_intro")}</strong>
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("privacy.page.overview_1")}</li>
                  <li>{t("privacy.page.overview_2")}</li>
                  <li>{t("privacy.page.overview_3")}</li>
                  <li>{t("privacy.page.overview_4")}</li>
                  <li>{t("privacy.page.overview_5")}</li>
                </ul>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* GDPR Notice */}
            <Card className="mt-12 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  {t("privacy.page.gdpr_title")}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  {t("privacy.page.gdpr_content")}
                </p>
                <p className="mt-4">
                  {t("privacy.page.gdpr_basis")}
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>{t("privacy.page.gdpr_consent")}</li>
                  <li>{t("privacy.page.gdpr_contract")}</li>
                  <li>{t("privacy.page.gdpr_interests")}</li>
                </ul>
              </CardContent>
            </Card>

            {/* California Privacy Rights */}
            <Card className="mt-8 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  {t("privacy.page.ccpa_title")}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  {t("privacy.page.ccpa_content")}
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>{t("privacy.page.ccpa_know")}</li>
                  <li>{t("privacy.page.ccpa_delete")}</li>
                  <li>{t("privacy.page.ccpa_opt_out")}</li>
                  <li>{t("privacy.page.ccpa_non_discrimination")}</li>
                </ul>
                <p className="mt-4">
                  <strong>{t("privacy.page.ccpa_note")}</strong>
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
