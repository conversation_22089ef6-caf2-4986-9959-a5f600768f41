import { Card, CardContent } from "@/components/ui/card";
import { type Testimonial } from "@/lib/types";

interface TestimonialCardProps {
  testimonial: Testimonial;
}

export function TestimonialCard({ testimonial }: TestimonialCardProps) {
  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardContent className="p-8">
        {testimonial.companyLogo ? (
          <img
            src={testimonial.companyLogo}
            alt={testimonial.company}
            className="h-12 mb-6"
          />
        ) : (
          <div className="h-12 mb-6 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-gray-600 font-bold text-lg">{testimonial.company}</span>
          </div>
        )}
        
        <blockquote className="text-gray-600 mb-6 italic">
          "{testimonial.quote}"
        </blockquote>
        
        <div className="flex items-center">
          {testimonial.authorAvatar ? (
            <img
              src={testimonial.authorAvatar}
              alt={testimonial.author}
              className="w-12 h-12 rounded-full mr-4"
            />
          ) : (
            <div className="w-12 h-12 rounded-full bg-gray-200 mr-4 flex items-center justify-center">
              <span className="text-gray-500 font-semibold">
                {testimonial.author.charAt(0)}
              </span>
            </div>
          )}
          <div>
            <div className="font-semibold">{testimonial.author}</div>
            <div className="text-sm text-gray-600">{testimonial.authorTitle}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
