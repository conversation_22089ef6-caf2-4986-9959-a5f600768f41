import { useState } from "react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { useTranslation, type Language } from "@/lib/i18n";
import { Menu, X, Scissors } from "lucide-react";

interface HeaderProps {
  language: Language;
  onLanguageChange: (language: Language) => void;
}

export function Header({ language, onLanguageChange }: HeaderProps) {
  const [location] = useLocation();
  const { t } = useTranslation(language);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navigation: { name: string; href: string }[] = [];

  return (
    <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <div className="gradient-bg rounded-lg p-2 mr-3">
              <Scissors className="h-5 w-5 text-white" />
            </div>
            <span className="text-2xl font-bold gradient-text">Remove bg</span>
          </Link>
          
          {/* Desktop Navigation - Empty since navigation items removed */}
          <nav className="hidden md:flex items-center space-x-8">
          </nav>
          
          {/* Language Switcher */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher language={language} onLanguageChange={onLanguageChange} />
            
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
        
        {/* Mobile Navigation - Hidden since navigation items removed */}
      </div>
    </header>
  );
}
