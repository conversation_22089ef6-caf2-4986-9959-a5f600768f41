import { useTranslation, type Language } from "@/lib/i18n";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, Phone, MapPin, Clock, ArrowLeft } from "lucide-react";
import { <PERSON> } from "wouter";
import { SEOHead } from "@/components/SEOHead";

interface ContactProps {
  language: Language;
}

export default function Contact({ language }: ContactProps) {
  const { t } = useTranslation(language);

  const contactInfo = [
    {
      icon: Mail,
      title: t("contact.email"),
      content: "<EMAIL>",
      description: t("contact.email_desc")
    },
    {
      icon: Phone,
      title: t("contact.phone"),
      content: "+****************",
      description: t("contact.phone_desc")
    },
    {
      icon: MapPin,
      title: t("contact.address"),
      content: "Remove.bg Inc.\n123 AI Street\nSan Francisco, CA 94107",
      description: t("contact.address_desc")
    },
    {
      icon: Clock,
      title: t("contact.hours"),
      content: t("contact.hours_content"),
      description: t("contact.hours_desc")
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("contact.back_to_home")}
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("contact.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("contact.description")}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">{t("contact.form_title")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("contact.first_name")}
                    </label>
                    <Input placeholder={t("contact.first_name_placeholder")} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("contact.last_name")}
                    </label>
                    <Input placeholder={t("contact.last_name_placeholder")} />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("contact.email_label")}
                  </label>
                  <Input type="email" placeholder={t("contact.email_placeholder")} />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("contact.subject")}
                  </label>
                  <Input placeholder={t("contact.subject_placeholder")} />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t("contact.message")}
                  </label>
                  <Textarea 
                    placeholder={t("contact.message_placeholder")}
                    className="min-h-[120px]"
                  />
                </div>
                
                <Button className="w-full gradient-bg text-white">
                  {t("contact.send_message")}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            {contactInfo.map((info, index) => {
              const IconComponent = info.icon;
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="gradient-bg rounded-lg p-3 flex-shrink-0">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-2">{info.title}</h3>
                        <p className="text-gray-900 font-medium mb-1 whitespace-pre-line">
                          {info.content}
                        </p>
                        <p className="text-gray-600 text-sm">{info.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}