import React from "react";
import { useLocation } from "wouter";
import { useTranslation, type Language } from "@/lib/i18n";
import {
  defaultSEOConfig,
  generateHreflangLinks,
  generateCanonicalUrl,
  generateJsonLd
} from "@/lib/seo";

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  language: Language;
  canonical?: string;
  ogImage?: string;
  jsonLd?: object;
}

export function SEOHead({
  title,
  description,
  keywords,
  language,
  canonical,
  ogImage,
  jsonLd
}: SEOHeadProps) {
  const [location] = useLocation();
  const { t } = useTranslation(language);

  const seoConfig = defaultSEOConfig[language];
  const defaultTitle = t("hero.title.line1") + " - " + t("hero.title.line2");
  const defaultDescription = t("hero.description");

  const finalTitle = title || seoConfig.title || defaultTitle;
  const finalDescription = description || seoConfig.description || defaultDescription;
  const finalKeywords = keywords || seoConfig.keywords;
  const finalCanonical = canonical || generateCanonicalUrl(location, language);
  const finalOgImage = ogImage || "https://www.remove.bg/og-image.jpg";
  const finalJsonLd = jsonLd || generateJsonLd(language, location);
  
  // Update document title and meta tags
  React.useEffect(() => {
    document.title = finalTitle;

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', finalDescription);
    }

    // Update meta keywords
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute('content', finalKeywords);
    } else {
      metaKeywords = document.createElement('meta');
      metaKeywords.setAttribute('name', 'keywords');
      metaKeywords.setAttribute('content', finalKeywords);
      document.head.appendChild(metaKeywords);
    }

    // Update language
    document.documentElement.setAttribute('lang', language);

    // Update canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', finalCanonical);

    // Remove existing hreflang links
    const existingHreflangLinks = document.querySelectorAll('link[rel="alternate"][hreflang]');
    existingHreflangLinks.forEach(link => link.remove());

    // Add hreflang links
    const hreflangLinks = generateHreflangLinks(location);
    hreflangLinks.forEach(({ hreflang, href }) => {
      const link = document.createElement('link');
      link.setAttribute('rel', 'alternate');
      link.setAttribute('hreflang', hreflang);
      link.setAttribute('href', href);
      document.head.appendChild(link);
    });

    // Update Open Graph tags
    let ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', finalTitle);
    }

    let ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      ogDescription.setAttribute('content', finalDescription);
    }

    let ogUrl = document.querySelector('meta[property="og:url"]');
    if (ogUrl) {
      ogUrl.setAttribute('content', finalCanonical);
    }

    let ogImage = document.querySelector('meta[property="og:image"]');
    if (ogImage) {
      ogImage.setAttribute('content', finalOgImage);
    } else {
      ogImage = document.createElement('meta');
      ogImage.setAttribute('property', 'og:image');
      ogImage.setAttribute('content', finalOgImage);
      document.head.appendChild(ogImage);
    }

    let ogLocale = document.querySelector('meta[property="og:locale"]');
    if (ogLocale) {
      ogLocale.setAttribute('content', language === 'zh' ? 'zh_CN' : language);
    } else {
      ogLocale = document.createElement('meta');
      ogLocale.setAttribute('property', 'og:locale');
      ogLocale.setAttribute('content', language === 'zh' ? 'zh_CN' : language);
      document.head.appendChild(ogLocale);
    }

    // Update Twitter Card tags
    let twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle) {
      twitterTitle.setAttribute('content', finalTitle);
    }

    let twitterDescription = document.querySelector('meta[name="twitter:description"]');
    if (twitterDescription) {
      twitterDescription.setAttribute('content', finalDescription);
    }

    let twitterImage = document.querySelector('meta[name="twitter:image"]');
    if (twitterImage) {
      twitterImage.setAttribute('content', finalOgImage);
    } else {
      twitterImage = document.createElement('meta');
      twitterImage.setAttribute('name', 'twitter:image');
      twitterImage.setAttribute('content', finalOgImage);
      document.head.appendChild(twitterImage);
    }

    // Update JSON-LD structured data
    let existingJsonLd = document.querySelector('script[type="application/ld+json"]#dynamic-jsonld');
    if (existingJsonLd) {
      existingJsonLd.remove();
    }

    const jsonLdScript = document.createElement('script');
    jsonLdScript.setAttribute('type', 'application/ld+json');
    jsonLdScript.setAttribute('id', 'dynamic-jsonld');
    jsonLdScript.textContent = JSON.stringify(finalJsonLd);
    document.head.appendChild(jsonLdScript);

  }, [finalTitle, finalDescription, finalKeywords, finalCanonical, finalOgImage, finalJsonLd, language, location]);
  
  return null;
}
