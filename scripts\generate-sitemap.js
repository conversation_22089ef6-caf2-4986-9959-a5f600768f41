import { writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const generateSitemapXML = () => {
  const baseUrl = "https://www.remove.bg";
  const languages = ['en', 'zh', 'es', 'fr', 'de'];
  const currentDate = new Date().toISOString().split('T')[0];
  
  const routes = [
    { path: '/', priority: 1.0, changefreq: 'daily' },
    { path: '/help', priority: 0.8, changefreq: 'weekly' },
    { path: '/contact', priority: 0.7, changefreq: 'monthly' },
    { path: '/status', priority: 0.6, changefreq: 'weekly' },
    { path: '/feedback', priority: 0.6, changefreq: 'monthly' },
    { path: '/privacy', priority: 0.6, changefreq: 'monthly' },
    { path: '/terms', priority: 0.6, changefreq: 'monthly' },
    { path: '/cookies', priority: 0.5, changefreq: 'monthly' },
    { path: '/imprint', priority: 0.5, changefreq: 'monthly' },
    { path: '/tools', priority: 0.8, changefreq: 'weekly' },
    { path: '/blog', priority: 0.7, changefreq: 'weekly' },
  ];

  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`;

  routes.forEach(route => {
    // Generate alternates for each route
    const alternates = languages.map(lang => ({
      hreflang: lang,
      href: lang === 'en' ? `${baseUrl}${route.path}` : `${baseUrl}/${lang}${route.path}`
    }));
    
    // Add x-default
    alternates.push({
      hreflang: 'x-default',
      href: `${baseUrl}${route.path}`
    });

    // Add URL for each language
    languages.forEach(lang => {
      const loc = lang === 'en' ? `${baseUrl}${route.path}` : `${baseUrl}/${lang}${route.path}`;
      
      xml += `  <url>
    <loc>${loc}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
`;

      alternates.forEach(alternate => {
        xml += `    <xhtml:link rel="alternate" hreflang="${alternate.hreflang}" href="${alternate.href}" />
`;
      });

      xml += `  </url>
`;
    });
  });

  xml += `</urlset>`;
  
  return xml;
};

// Generate and save sitemap
const sitemapXML = generateSitemapXML();
const outputPath = join(__dirname, '../client/public/sitemap.xml');

try {
  writeFileSync(outputPath, sitemapXML, 'utf8');
  console.log('✅ Sitemap generated successfully at:', outputPath);
} catch (error) {
  console.error('❌ Error generating sitemap:', error);
  process.exit(1);
}
