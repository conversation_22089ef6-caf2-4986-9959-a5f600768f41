import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { FileText, AlertTriangle, CreditCard, Shield, Users, Gavel, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface TermsProps {
  language: Language;
}

export default function Terms({ language }: TermsProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "acceptance",
      title: t("terms.section.acceptance.title"),
      icon: <FileText className="h-6 w-6" />,
      content: t("terms.section.acceptance.content")
    },
    {
      id: "description",
      title: t("terms.section.description.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("terms.section.description.content")
    },
    {
      id: "user-accounts",
      title: t("terms.section.user_accounts.title"),
      icon: <Users className="h-6 w-6" />,
      content: t("terms.section.user_accounts.content")
    },
    {
      id: "acceptable-use",
      title: t("terms.section.acceptable_use.title"),
      icon: <AlertTriangle className="h-6 w-6" />,
      content: t("terms.section.acceptable_use.content")
    },
    {
      id: "intellectual-property",
      title: t("terms.section.intellectual_property.title"),
      icon: <Gavel className="h-6 w-6" />,
      content: t("terms.section.intellectual_property.content")
    },
    {
      id: "payment-terms",
      title: t("terms.section.payment.title"),
      icon: <CreditCard className="h-6 w-6" />,
      content: t("terms.section.payment.content")
    },
    {
      id: "limitation",
      title: t("terms.section.limitation.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("terms.section.limitation.content")
    },
    {
      id: "termination",
      title: t("terms.section.termination.title"),
      icon: <AlertTriangle className="h-6 w-6" />,
      content: t("terms.section.termination.content")
    }
  ];

  return (
    <>
      <SEOHead
        title="Terms of Service - Remove bg"
        description="Read our Terms of Service to understand the rules and guidelines for using Remove bg's AI-powered background removal service."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t("terms.page.back")}
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <FileText className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                {t("terms.page.title")}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t("terms.page.description")}
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>{t("terms.page.last_updated")}</p>
                <p>{t("terms.page.effective_date")}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Terms Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Summary */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">{t("terms.page.quick_summary")}</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  <strong>{t("terms.page.summary_intro")}</strong>
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("terms.page.summary_1")}</li>
                  <li>{t("terms.page.summary_2")}</li>
                  <li>{t("terms.page.summary_3")}</li>
                  <li>{t("terms.page.summary_4")}</li>
                  <li>{t("terms.page.summary_5")}</li>
                  <li>{t("terms.page.summary_6")}</li>
                </ul>
                <p className="text-sm text-gray-500 italic">
                  {t("terms.page.summary_note")}
                </p>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Information */}
            <Card className="mt-12 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  {t("terms.page.questions_title")}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  {t("terms.page.questions_text")}
                </p>
                <div className="mt-4 space-y-2">
                  <p><strong>{t("terms.page.contact_email")}</strong></p>
                  <p><strong>{t("terms.page.contact_address")}</strong></p>
                </div>
                <p className="mt-4 text-sm">
                  {t("terms.page.response_time")}
                </p>
              </CardContent>
            </Card>

            {/* Governing Law */}
            <Card className="mt-8 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  Governing Law
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  These Terms shall be interpreted and governed by the laws of the State of California, United States, 
                  without regard to its conflict of law provisions.
                </p>
                <p className="mt-4">
                  Any disputes arising from these Terms or your use of the Service will be resolved through binding 
                  arbitration in accordance with the rules of the American Arbitration Association.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
