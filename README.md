# RemoveBg Web Application

A web application for removing backgrounds from images using AI technology.

## Features

- **Multi-format Image Support**: Supports JPG, JPEG, PNG, GIF, WebP, BMP, TIFF, and SVG formats
- **Drag & Drop Upload**: Easy image upload with drag and drop functionality
- **Real-time Processing**: Background removal with status tracking
- **Demo Mode**: Works without API key for testing purposes
- **Responsive Design**: Works on desktop and mobile devices

## Supported Image Formats

- JPG/JPEG
- PNG
- GIF
- WebP
- BMP
- TIFF/TIF
- SVG

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Configuration (Optional)

For real background removal, you need a remove.bg API key:

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Get your API key from [remove.bg](https://www.remove.bg/api)

3. Add your API key to the `.env` file:
   ```
   REMOVE_BG_API_KEY=your_api_key_here
   ```

### 3. Run the Application

```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## Usage

### Demo Mode (No API Key)
- Upload any supported image format
- The app will simulate background removal by copying the original image
- Perfect for testing the upload functionality and UI

### Production Mode (With API Key)
- Configure your remove.bg API key in the `.env` file
- Upload images to get real AI-powered background removal
- Processed images will have transparent backgrounds

## File Upload Limits

- Maximum file size: 10MB
- Supported formats: JPG, JPEG, PNG, GIF, WebP, BMP, TIFF, SVG

## API Endpoints

- `POST /api/upload` - Upload an image for processing
- `GET /api/process/:id` - Check processing status
- `GET /uploads/:filename` - Download processed images

## Development

### Project Structure

```
├── client/          # Frontend React application
├── server/          # Backend Express server
├── shared/          # Shared types and schemas
├── uploads/         # Uploaded and processed images
└── test-upload.html # Test page for upload functionality
```

### Testing Upload Functionality

Visit `http://localhost:5000/test-upload.html` to test the upload functionality with detailed feedback.

## Troubleshooting

### "Processing failed" Error
This usually means:
1. No remove.bg API key is configured (app will use demo mode)
2. Invalid API key
3. API rate limit exceeded
4. Network connectivity issues

### Upload Issues
- Check file format is supported
- Ensure file size is under 10MB
- Check server logs for detailed error messages

## License

MIT License
